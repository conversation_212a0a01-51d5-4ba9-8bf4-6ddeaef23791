{"name": "tts-vue-web", "version": "v2.3.0", "description": "🎤 微软语音合成工具，Web版本，使用 Vue + ElementPlus + Vite 构建。", "author": "Anger <<EMAIL>>", "license": "MIT", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview"}, "engines": {"node": ">=14.17.0"}, "devDependencies": {"@types/node": "^24.0.7", "@vitejs/plugin-vue": "4.5.2", "@vue/runtime-core": "3.5.11", "typescript": "^5.8.3", "vite": "4.5.2", "vue": "3.5.11", "vue-tsc": "^2.2.10"}, "env": {"VITE_DEV_SERVER_HOST": "127.0.0.1", "VITE_DEV_SERVER_PORT": "3344"}, "keywords": ["vite", "vue3", "vue"], "dependencies": {"axios": "0.27.2", "element-plus": "^2.9.9", "openai": "^4.0.0", "pinia": "3.0.2", "uuid": "8.3.2", "vue-demi": "^0.14.10", "vue-i18n": "9.6.5", "vue-router": "^4.5.1"}}