# 依赖相关
node_modules
.pnpm-store/

# 编译输出
dist
dist-ssr
*.local

# 文档
src/docs/

# 日志
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# 编辑器目录和文件
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# 环境变量
.env
.env.*
!.env.example

# 测试覆盖率
coverage
.nyc_output

# 缓存
.eslintcache
.stylelintcache
.temp
.cache

# 构建和发布
release/*.zip
release/*.exe
release/*.dmg
release/*.AppImage
!release/*/
