# TTS-Vue Web版本

🎤 微软语音合成工具，Web版本，使用 `Vue` + `ElementPlus` + `Vite` 构建。

## 网站示例

https://web.tts88.top
### 新版本界面
![image](https://github.com/user-attachments/assets/177c8c0d-33d1-48e7-81e3-778f36d8fedd)

### 旧版本界面
![image](https://github.com/user-attachments/assets/67cafd2c-7b0f-4b0d-b14b-bf402aaff0cd)

## 功能特点

- 🌐 完全Web化，无需安装桌面应用
- 🔊 支持微软多种语音合成
- 🚀 支持TTS88 API集成
- 🆓 支持免费TTS调用，每日免费额度
- 🧠 支持OpenAI的文本生成
- 🌍 支持多语言：中文、英文、西班牙语
- 🔐 浏览器指纹识别，更安全的用户体验

## 最新更新

### 界面重构与功能增强 (v2.2.0)

- 📱 **UI重构**：固定顶部导航，文本框采用吸顶模式，优化移动端适配
- 🔤 **SSML增强**：新增SSML格式化功能，自动根据设置变化更新SSML
- 🎵 **播放器优化**：集成播放器到主界面，改进音频播放和下载体验
- 🎞️ **字幕功能**：新增在线生成字幕功能，提升内容创作效率
- 🌓 **主题优化**：改善暗黑模式下的界面表现，增加全局主题切换
- 🛠️ **高级设置**：优化免费TTS服务界面，新增语速和音调配置
- 💡 **交互体验**：添加工具提示功能，增强用户操作指引
- 🔄 **依赖升级**：Vue更新至3.5.11，ElementPlus更新至2.9.9
- 🌐 **链接更新**：更新GitHub仓库地址

### 免费TTS服务集成 (v2.1.0)

- 🆕 **免费TTS功能**：无需API密钥，每日都有免费额度
- 👤 **浏览器指纹识别**：保证每个用户公平使用免费额度
- 🔄 **额度状态显示**：实时显示剩余免费字符数和重置时间
- 🌐 **服务器状态检测**：自动检测免费TTS服务器连接状态
- 🛡️ **增强错误处理**：更友好的错误提示和状态反馈
- 🎛️ **免费TTS设置面板**：方便用户配置和查看额度信息

### 界面与用户体验优化 (v2.0.0)

- ✨ **设置区域优化**：设置面板支持折叠，点击"语音设置"标题即可展开/折叠
- 🔘 **智能按钮位置**："开始转换"按钮移至顶部，无需滚动即可点击
- 💬 **状态提示增强**：添加直观的转换状态提示文字
- 🎨 **视觉效果改进**：按钮添加光效动画，增强用户体验
- 📱 **响应式布局优化**：改进在不同屏幕尺寸下的显示效果

这些改进大大提升了使用效率，特别是解决了传统设计中用户需要滚动到页面底部才能点击"开始转换"按钮的问题。现在，无论用户在页面的哪个位置，都可以轻松启动转换过程。

## 开发计划

> **📢 重要通知：桌面版本正在开发中！**
> 我们正在开发跨平台桌面应用版本，将支持更多功能和更好的用户体验。
> 敬请期待后续更新，请关注项目动态获取最新信息。

## 快速开始

### 开发环境

```bash
# 克隆仓库
git clone https://github.com/henryhu55/tts-web-vue.git

# 安装依赖
yarn install

# 启动开发服务器
yarn dev
```

### 生产构建

```bash
# 构建生产版本
yarn build

# 预览生产版本
yarn preview
```

## 部署

构建完成后，将 `dist`目录的内容部署到任何静态Web服务器上即可。

## API配置

本Web版本目前支持免费TTS服务、TTS88 API和OpenAI API:

### 免费TTS服务配置

1. 在设置页面中找到"免费TTS服务"选项
2. 系统默认配置了免费TTS服务器地址
3. 您可以查看当日剩余免费额度和重置时间
4. 每个浏览器客户端拥有独立的免费额度

### TTS88 API配置

1. 在设置页面中找到"第三方API URL"设置选项
2. 输入您的TTS88 API地址
3. 如果有API密钥，请输入到"TTS88 API密钥"字段

### OpenAI API配置

1. 在设置页面中找到OpenAI设置选项
2. 输入您的OpenAI API密钥
3. 选择要使用的模型（默认为gpt-3.5-turbo）
4. 如果使用自托管或代理，可以设置自定义的API Base URL

## 注意事项

- 数据仅存储在浏览器本地存储中，刷新或清除缓存不会影响到其他用户
- 转换后的音频文件可以直接在浏览器中播放或下载到本地
- 免费TTS服务每日有使用额度限制，超出需等待次日重置

## 技术栈

- Vue 3.2
- Pinia
- ElementPlus
- Vite

## 许可证

MIT License

## 开始使用

- [项目简介](https://docs.tts88.top//guide/intro.html)
- [安装运行](https://docs.tts88.top//guide/install.html)
- [功能介绍](https://docs.tts88.top/guide/features.html)
- [常见问题](https://docs.tts88.top//guide/qa.html)
- [更新日志](https://docs.tts88.top//guide/update.html)

## 注意

该软件以及代码仅为个人学习测试使用，请在下载后24小时内删除，不得用于商业用途，否则后果自负。任何违规使用造成的法律后果与本人无关。该软件也永远不会收费，如果您使用该软件前支付了额外费用，或付费获得源码或成品软件，那么你一定被骗了！

**搬运请注明出处。禁止诱导他人以加群、私信等方式获取软件的仓库、下载地址和安装包。**

### 意见问题反馈，版本发布企鹅群：

`【tts-web-vue问题反馈群1】279895662`
