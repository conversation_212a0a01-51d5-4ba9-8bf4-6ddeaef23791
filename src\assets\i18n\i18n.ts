// src/assets/i18n/i18n.ts
// 使用TypeScript的非空断言
// @ts-ignore
import * as VueI18n from 'vue-i18n'
// @ts-ignore
const createI18n = VueI18n.createI18n

const messages = {
  en: {
    // Mensajes en inglés
    aside: {
      text: "Text",
      batch: "Batch",
      settings: "Settings",
      documents: "Documents",
    },
    messages: {
      saveConfig: "Save Configuration",
      saveConfigPrompt: "Please enter configuration name",
      invalidInput: "Invalid input",
      saveSuccess: "Configuration saved successfully",
      cancelSave: "Save cancelled",
      inputWarning: "Please enter text content",
      emptyListWarning: "The list is empty"
    },
    buttons: {
      confirm: "Confirm",
      cancel: "Cancel"
    },
    version: {
      checkUpdate: "Check for updates",
      currentVersion: "Current Version:",
      latestVersion: "Latest Version:",
      updateAvailable: "Update Available",
      noUpdate: "You are up to date!",
      updateInfo: "Update Information",
      confirm: "OK",
      downloadLinks: "Download Links",
      password: "Password: em1n",
    },
    bilibtn: {
      goToBilibili: "Go to Bilibili",
    },
    configPage: {
      downloadPath: "Download Path",
      retryCount: "Retry Count",
      retryInterval: "Retry Interval (s)",
      speechKey: "SpeechKey Azure",
      serviceRegion: "ServiceRegion Azure",
      thirdPartyApi: "TTS88 API",
      thirdPartyApiPlaceholder: "e.g. https://tts88.top/cognitiveservices/v1",
      tts88Key: "TTS88 KEY",
      tts88KeyPlaceholder: "Please enter TTS88 KEY",
      autoplay: "Autoplay",
      language: "Language",
      updateNotification: "Update Notification",
      titleStyle: "Title Bar Style",
      auditionText: "Audition Text",
      templateEdit: "Template Edit",
      name: "Name",
      action: "Action",
      delete: "Delete",
      refreshConfig: "Refresh Configuration",
      configFile: "Configuration File",
      openLogs: "Open Logs",
      clearLogs: "Clear Logs",
      yes: "Yes",
      no: "No",
      serviceRegionPlaceHolder: "Fill in the service region, such as: westus",
      confirm: "OK",
      voice: "Voice",
      style: "Style",
      role: "Role",
      speed: "Speed",
      pitch: "Pitch",
      remove: "Remove",
      openAIKey: "OpenAI Key",
      openAIBaseUrl: "OpenAI Base URL",
      openAIBaseUrlPlaceholder: "e.g. https://api.chatweb.plus/v1",
      gptModel: "Model GPT",
      // Otras traducciones...
    },
    donate: {
      appreciation: "If you think this project is good,",
      encouragement:
        "Feel free to Star, Fork, and PR. Your Star is the best encouragement for the author :)",
      guideReminder:
        'If you encounter any problems, please read carefully the "Documentation" → "User Guide" section, including "Feature Introduction" and "FAQ".',
      feedback:
        'For other opinions or suggestions, you can @mention or privately chat with the group owner or manager in "Documentation" → "Join Q Group", or raise issues on GitHub or Gitee.',
      buyCoffeeTitle: "Buy the author a coffee 🍻",
      wechatPayment: "Use WeChat for payment",
      hoverForAlipay: "Hover for Alipay payment",
      buyDrinkTitle: "Buy the author a drink ☕️",
      alipayPayment: "Use Alipay for payment",
      hoverForWechat: "Move the mouse away for WeChat payment",
    },

    footer: {
      downloadAudio: "Download Audio",
      format: "Format",
      // Otras traducciones...
    },
    styles: {
      assistant: "Assistant",
      chat: "Chat",
      customerservice: "Customer Service",
      newscast: "Newscast",
      affectionate: "Affectionate",
      angry: "Angry",
      calm: "Calm",
      cheerful: "Cheerful",
      disgruntled: "Disgruntled",
      fearful: "Fearful",
      gentle: "Gentle",
      lyrical: "Lyrical",
      sad: "Sad",
      serious: "Serious",
      "poetry-reading": "Poetry Reading",
      "narration-professional": "Professional Narration",
      "newscast-casual": "Casual Newscast",
      embarrassed: "Embarrassed",
      depressed: "Depressed",
      envious: "Envious",
      "narration-relaxed": "Relaxed Narration",
      Advertisement_upbeat: "Upbeat Advertisement",
      "Narration-relaxed": "Relaxed Narration",
      Sports_commentary: "Sports Commentary",
      Sports_commentary_excited: "Excited Sports Commentary",
      "documentary-narration": "Documentary Narration",
      excited: "Excited",
      friendly: "Friendly",
      terrified: "Terrified",
      shouting: "Shouting",
      unfriendly: "Unfriendly",
      whispering: "Whispering",
      hopeful: "Hopeful",
    },
    roles: {
      YoungAdultFemale: "Young Adult Female",
      YoungAdultMale: "Young Adult Male",
      OlderAdultFemale: "Older Adult Female",
      OlderAdultMale: "Older Adult Male",
      SeniorFemale: "Senior Female",
      SeniorMale: "Senior Male",
      Girl: "Girl",
      Boy: "Boy",
      Narrator: "Narrator",
    },
    main: {
      titleGenerateTextGPT: "Generate Text with GPT",
      descriptionGenerateTextGPT:
        "Generate text with GPT-3 or GPT-4, the most powerful AI model in the world.",
      placeholderGPT: "Please enter the prompt text",
      action: "Action",
      textTab: "Text",
      ssmlTab: "SSML",
      placeholder: "Please input",
      fileName: "File Name",
      filePath: "File Path",
      fileSize: "Word Count",
      status: "Status",
      ready: "Ready",
      remove: "Remove",
      play: "Play",
      openInFolder: "Open in Folder",
      selectFiles: "Select Files",
      fileFormatTip: "The format of text: *.txt",
      clearAll: "Clear All",
      doc: "Documentation",
      textRequired: "Please enter text content",
      pleaseWait: "Please wait...",
      converting: "Converting...",
      conversionFailed: "Conversion failed"
    },
    options: {
      api: "Interface",
      selectApi: "Select API",
      language: "Language",
      selectLanguage: "Select Language",
      voice: "Voice",
      selectVoice: "Select Voice",
      speakingStyle: "Speaking Style",
      selectSpeakingStyle: "Select Speaking Style",
      rolePlaying: "Role Playing",
      selectRole: "Select Role",
      speed: "Speed",
      pitch: "Pitch",
      saveConfig: "Save Configuration",
      selectConfig: "Select Configuration",
      startConversion: "Start Conversion",
      edgeApiWarning:
        "The Edge interface does not support automatic slicing and the maximum text length is unknown. Please preprocess text manually as needed.",
      configureAzure:
        "Please configure Azure's Speech service key and region first.",
      saveSuccess: "Configuration saved successfully.",
      cancelSave: "Save cancelled.",
      inputWarning: "Please enter text content.",
      emptyListWarning: "The list is empty.",
      waitMessage: "Please wait...",
      intensity: "Intensity",
      selectIntensity: "Select Intensity",
      default: "Default",
      weak: "Weak",
      normal: "Normal",
      strong: "Strong",
      extraStrong: "Extra Strong",
      silence: "Silence",
      selectSilence: "Select Silence",
      defaultSilence: "Default",
      volume: "Volume",
      selectVolume: "Select Volume",
      extraWeak: "Extra Weak",
      preset: "Preset",
      selectPreset: "Select Preset",
      presetDefault: "Default",
      presetNews: "News Cast",
      presetStory: "Storytelling",
      presetPoetry: "Poetry Reading",
      presetExcited: "Excited",
      presetSad: "Sad",
      presetBusiness: "Business Professional",
      presetFriendly: "Friendly",
      presetCustomerService: "Customer Service",
      presetAdvertisement: "Advertisement",
      presetApplied: "Preset Configuration Applied",
      voiceSettings: "Voice Settings",
      advancedSettings: "Advanced Settings",
      saveAsPreset: "Save as Preset",
      resetToDefault: "Reset to Default",
      xSoft: "Extra Soft",
      soft: "Soft",
      medium: "Medium",
      loud: "Loud",
      xLoud: "Extra Loud",
      readyToConvert: "Ready to convert?",
      converting: "Converting...",
      switchToAnchors: "Switch to Anchors Mode",
      switchToCustom: "Switch to Custom Mode",
      searchVoice: "Search Voice Anchors",
      search: "Search",
      voiceAnchor: "Voice Anchor",
      selectAnchor: "Select Anchor",
      anchorPreview: "Preview",
      anchorPro: "PRO",
      anchorSelected: "Anchor Selected",
      audioFormat: "Audio Format",
      selectAudioFormat: "Select Audio Format",
      audioQuality: "Audio Quality",
      selectAudioQuality: "Select Audio Quality",
      standardQuality: "Standard Quality",
      highQuality: "High Quality",
      ultraQuality: "Ultra High Quality",
      autoPreview: "Auto Preview",
      autoPreviewDesc: "Auto play preview after adjusting settings",
      batchDownload: "Batch Download",
      batchDownloadDesc: "Download all generated audio files",
      saveToCloud: "Save to Cloud",
      saveToCloudDesc: "Save audio to cloud for easy access",
      formatMP3: "MP3 Format",
      formatWAV: "WAV Format",
      formatOGG: "OGG Format",
      formatFLAC: "FLAC Format"
    },
    lang: {
      AF_ZA: "Afrikaans (South Africa)",
      AM_ET: "Amharic (Ethiopia)",
      AR_AE: "Arabic (United Arab Emirates)",
      AR_BH: "Arabic (Bahrain)",
      AR_DZ: "Arabic (Algeria)",
      AR_EG: "Arabic (Egypt)",
      AR_IL: "Arabic (Israel)",
      AR_IQ: "Arabic (Iraq)",
      AR_JO: "Arabic (Jordan)",
      AR_KW: "Arabic (Kuwait)",
      AR_LB: "Arabic (Lebanon)",
      AR_LY: "Arabic (Libya)",
      AR_MA: "Arabic (Morocco)",
      AR_OM: "Arabic (Oman)",
      AR_PS: "Arabic (Palestinian Authority)",
      AR_QA: "Arabic (Qatar)",
      AR_SA: "Arabic (Saudi Arabia)",
      AR_SY: "Arabic (Syria)",
      AR_TN: "Arabic (Tunisia)",
      AR_YE: "Arabic (Yemen)",
      AS_IN: "Assamese (India)",
      AZ_AZ: "Azerbaijani (Azerbaijan)",
      BG_BG: "Bulgarian (Bulgaria)",
      BN_BD: "Bengali (Bangladesh)",
      BN_IN: "Bengali (India)",
      BS_BA: "Bosnian (Bosnia and Herzegovina)",
      CA_ES: "Catalan (Spain)",
      CS_CZ: "Czech (Czech Republic)",
      CY_GB: "Welsh (United Kingdom)",
      DA_DK: "Danish (Denmark)",
      DE_AT: "German (Austria)",
      DE_CH: "German (Switzerland)",
      DE_DE: "German (Germany)",
      EL_GR: "Greek (Greece)",
      EN_AU: "English (Australia)",
      EN_CA: "English (Canada)",
      EN_GB: "English (United Kingdom)",
      EN_GH: "English (Ghana)",
      EN_HK: "English (Hong Kong SAR)",
      EN_IE: "English (Ireland)",
      EN_IN: "English (India)",
      EN_KE: "English (Kenya)",
      EN_NG: "English (Nigeria)",
      EN_NZ: "English (New Zealand)",
      EN_PH: "English (Philippines)",
      EN_SG: "English (Singapore)",
      EN_TZ: "English (Tanzania)",
      EN_US: "English (United States)",
      EN_ZA: "English (South Africa)",
      ES_AR: "Spanish (Argentina)",
      ES_BO: "Spanish (Bolivia)",
      ES_CL: "Spanish (Chile)",
      ES_CO: "Spanish (Colombia)",
      ES_CR: "Spanish (Costa Rica)",
      ES_CU: "Spanish (Cuba)",
      ES_DO: "Spanish (Dominican Republic)",
      ES_EC: "Spanish (Ecuador)",
      ES_ES: "Spanish (Spain)",
      ES_GQ: "Spanish (Equatorial Guinea)",
      ES_GT: "Spanish (Guatemala)",
      ES_HN: "Spanish (Honduras)",
      ES_MX: "Spanish (Mexico)",
      ES_NI: "Spanish (Nicaragua)",
      ES_PA: "Spanish (Panama)",
      ES_PE: "Spanish (Peru)",
      ES_PR: "Spanish (Puerto Rico)",
      ES_PY: "Spanish (Paraguay)",
      ES_SV: "Spanish (El Salvador)",
      ES_US: "Spanish (United States)",
      ES_UY: "Spanish (Uruguay)",
      ES_VE: "Spanish (Venezuela)",
      ET_EE: "Estonian (Estonia)",
      EU_ES: "Basque (Basque)",
      FA_IR: "Persian (Iran)",
      FIL_PH: "Filipino (Philippines)",
      FI_FI: "Finnish (Finland)",
      FR_BE: "French (Belgium)",
      FR_CA: "French (Canada)",
      FR_CH: "French (Switzerland)",
      FR_FR: "French (France)",
      GA_IE: "Irish (Ireland)",
      GL_ES: "Galician (Galicia)",
      GU_IN: "Gujarati (India)",
      HE_IL: "Hebrew (Israel)",
      HI_IN: "Hindi (India)",
      HR_HR: "Croatian (Croatia)",
      HU_HU: "Hungarian (Hungary)",
      HY_AM: "Armenian (Armenia)",
      ID_ID: "Indonesian (Indonesia)",
      IS_IS: "Icelandic (Iceland)",
      IT_CH: "Italian (Switzerland)",
      IT_IT: "Italian (Italy)",
      JA_JP: "Japanese (Japan)",
      JV_ID: "Javanese (Indonesia)",
      KA_GE: "Georgian (Georgia)",
      KK_KZ: "Kazakh (Kazakhstan)",
      KM_KH: "Khmer (Cambodia)",
      KN_IN: "Kannada (India)",
      KO_KR: "Korean (South Korea)",
      LO_LA: "Lao (Laos)",
      LT_LT: "Lithuanian (Lithuania)",
      LV_LV: "Latvian (Latvia)",
      MK_MK: "Macedonian (North Macedonia)",
      ML_IN: "Malayalam (India)",
      MN_MN: "Mongolian (Mongolia)",
      MR_IN: "Marathi (India)",
      MS_MY: "Malay (Malaysia)",
      MT_MT: "Maltese (Malta)",
      MY_MM: "Burmese (Myanmar)",
      NB_NO: "Norwegian Bokmål (Norway)",
      NE_NP: "Nepali (Nepal)",
      NL_BE: "Dutch (Belgium)",
      NL_NL: "Dutch (Netherlands)",
      OR_IN: "Odia (India)",
      PA_IN: "Punjabi (India)",
      PL_PL: "Polish (Poland)",
      PS_AF: "Pashto (Afghanistan)",
      PT_BR: "Portuguese (Brazil)",
      PT_PT: "Portuguese (Portugal)",
      RO_MD: "Romanian (Moldova)",
      RO_RO: "Romanian (Romania)",
      RU_RU: "Russian (Russia)",
      SI_LK: "Sinhala (Sri Lanka)",
      SK_SK: "Slovak (Slovakia)",
      SL_SI: "Slovenian (Slovenia)",
      SO_SO: "Somali (Somalia)",
      SQ_AL: "Albanian (Albania)",
      SR_ME: "Serbian (Cyrillic, Montenegro)",
      SR_RS: "Serbian (Serbia)",
      SR_XK: "Serbian (Cyrillic, Kosovo)",
      SU_ID: "Sundanese (Indonesia)",
      SV_SE: "Swedish (Sweden)",
      SW_KE: "Swahili (Kenya)",
      SW_TZ: "Swahili (Tanzania)",
      TA_IN: "Tamil (India)",
      TA_LK: "Tamil (Sri Lanka)",
      TA_MY: "Tamil (Malaysia)",
      TA_SG: "Tamil (Singapore)",
      TE_IN: "Telugu (India)",
      TH_TH: "Thai (Thailand)",
      TR_TR: "Turkish (Turkey)",
      UK_UA: "Ukrainian (Ukraine)",
      UR_IN: "Urdu (India)",
      UR_PK: "Urdu (Pakistan)",
      UZ_UZ: "Uzbek (Uzbekistan)",
      VI_VN: "Vietnamese (Vietnam)",
      WUU_CN: "Chinese (Wu, Simplified)",
      X_CUSTOM: "Custom Language",
      YUE_CN: "Chinese (Cantonese, Simplified)",
      ZH_CN: "Chinese (Mandarin, Simplified)",
      ZH_CN_Bilingual: "Chinese (Mandarin, Simplified), English Bilingual",
      ZH_CN_HENAN: "Chinese (Central Plains Henan, Simplified)",
      ZH_CN_LIAONING: "Chinese (Northeastern Mandarin, Simplified)",
      ZH_CN_SHAANXI: "Chinese (Central Plains Shaanxi, Simplified)",
      ZH_CN_SHANDONG: "Chinese (Ji–Lu Mandarin, Simplified)",
      ZH_CN_SICHUAN: "Chinese (Southwestern Mandarin, Simplified)",
      ZH_HK: "Chinese (Cantonese, Traditional)",
      ZH_TW: "Chinese (Taiwan Mandarin)",
      ZU_ZA: "Zulu (South Africa)",
      nalytics: "Language analysis",
      onversationAnalysisPreviewHint:
        "The call summary is currently a closed public preview and is only available for approved resources.",
      fAudio: "Language of audio",
      esource: "Language resource",
      echnologiesUsed: "Language technologies used",
      InPreview: "Language in preview",
    },
    initialLocalStore: {
      audition:
        "If you think this project is good, Star, Fork and PR are welcome. Your Star is the best encouragement to the author.",
    },
  },
  es: {
    // Mensajes en español
    aside: {
      text: "Texto",
      batch: "Lote",
      settings: "Configuración",
      documents: "Documentos",
    },
    version: {
      checkUpdate: "Buscar actualizaciones",
      currentVersion: "Versión Actual:",
      latestVersion: "Última Versión:",
      updateAvailable: "Actualización disponible",
      noUpdate: "¡Estás actualizado!",
      updateInfo: "Información de la actualización",
      confirm: "OK",
      downloadLinks: "Enlaces de Descarga",
      password: "Contraseña: em1n",
    },
    bilibtn: {
      goToBilibili: "Ir a Bilibili",
    },
    donate: {
      appreciation: "Si piensas que este proyecto es bueno,",
      encouragement:
        "No dudes en dar Star, hacer Fork y PR. Tu Star es el mejor ánimo para el autor :)",
      guideReminder:
        'Si encuentras algún problema, por favor lee detenidamente la sección "Documentación" → "Guía del Usuario", incluyendo "Introducción de Funciones" y "Preguntas Frecuentes".',
      feedback:
        'Para otras opiniones o sugerencias, puedes mencionar o chatear en privado con el dueño del grupo o el administrador en "Documentación" → "Unirse al Grupo Q", o plantear problemas en GitHub o Gitee.',
      buyCoffeeTitle: "Compra al autor un café 🍻",
      wechatPayment: "Usa WeChat para el pago",
      hoverForAlipay: "Pasa el ratón para pagar con Alipay",
      buyDrinkTitle: "Compra al autor una bebida ☕️",
      alipayPayment: "Usa Alipay para el pago",
      hoverForWechat: "Aleja el ratón para usar WeChat para el pago",
    },
    configPage: {
      downloadPath: "Ruta de Descarga",
      retryCount: "Recuento de Reintentos",
      retryInterval: "Intervalo de Reintento (s)",
      speechKey: "SpeechKey Azure",
      serviceRegion: "ServiceRegion Azure",
      thirdPartyApi: "API de Terceros",
      thirdPartyApiPlaceholder: "e.g. https://tts88.top/cognitiveservices/v1",
      tts88Key: "TTS88 KEY",
      tts88KeyPlaceholder: "Por favor ingrese TTS88 KEY",
      autoplay: "Reproducción Automática",
      language: "Idioma",
      updateNotification: "Notificación de Actualización",
      titleStyle: "Estilo de la Barra de Título",
      auditionText: "Texto de Audición",
      templateEdit: "Edición de Plantilla",
      name: "Nombre",
      action: "Acción",
      delete: "Eliminar",
      refreshConfig: "Refrescar Configuración",
      configFile: "Archivo Configuración",
      openLogs: "Abrir Registros",
      clearLogs: "Limpiar Registros",
      yes: "Sí",
      no: "No",
      serviceRegionPlaceHolder:
        "Complete la región de servicio, como por ejemplo: westus",
      confirm: "OK",
      voice: "Voz",
      style: "Estilo",
      role: "Rol",
      speed: "Velocidad",
      pitch: "Tono",
      remove: "Eliminar",
      openAIKey: "OpenAI key",
      openAIBaseUrl: "OpenAI Base URL",
      openAIBaseUrlPlaceholder: "e.g. https://api.chatweb.plus/v1",
      gptModel: "Modelo GPT",
      // Otras traducciones...
    },
    footer: {
      downloadAudio: "Descargar Audio",
      format: "Formato",
      // Otras traducciones...
    },
    styles: {
      assistant: "Asistente",
      chat: "Charla",
      customerservice: "Servicio al Cliente",
      newscast: "Noticiero",
      affectionate: "Cariñoso",
      angry: "Enojado",
      calm: "Tranquilo",
      cheerful: "Alegre",
      disgruntled: "Disgustado",
      fearful: "Temeroso",
      gentle: "Suave",
      lyrical: "Lírico",
      sad: "Triste",
      serious: "Serio",
      "poetry-reading": "Lectura de Poesía",
      "narration-professional": "Narración Profesional",
      "newscast-casual": "Noticiero Informal",
      embarrassed: "Avergonzado",
      depressed: "Deprimido",
      envious: "Envidioso",
      "narration-relaxed": "Narración Relajada",
      Advertisement_upbeat: "Publicidad Optimista",
      "Narration-relaxed": "Narración Relajada",
      Sports_commentary: "Comentario Deportivo",
      Sports_commentary_excited: "Comentario Deportivo Emocionado",
      "documentary-narration": "Narración de Documentales",
      excited: "Emocionado",
      friendly: "Amigable",
      terrified: "Aterrorizado",
      shouting: "Gritando",
      unfriendly: "Antipático",
      whispering: "Susurrando",
      hopeful: "Esperanzado",
    },
    roles: {
      YoungAdultFemale: "Mujer Joven Adulta",
      YoungAdultMale: "Hombre Joven Adulto",
      OlderAdultFemale: "Mujer Adulta Mayor",
      OlderAdultMale: "Hombre Adulto Mayor",
      SeniorFemale: "Mujer Senior",
      SeniorMale: "Hombre Senior",
      Girl: "Niña",
      Boy: "Niño",
      Narrator: "Narrador",
    },
    main: {
      titleGenerateTextGPT: "Genera Texto con GPT",
      descriptionGenerateTextGPT:
        "Genera texto con GPT-3 o GPT-4, el modelo de IA más potente del mundo.",
      placeholderGPT: "Por favor ingrese el texto de la sugerencia",
      action: "Acción",
      textTab: "Texto",
      ssmlTab: "SSML",
      placeholder: "Por favor ingrese",
      fileName: "Nombre de Archivo",
      filePath: "Ruta de Archivo",
      fileSize: "Palabras",
      status: "Estado",
      ready: "Listo",
      remove: "Eliminar",
      play: "Reproducir",
      openInFolder: "Abrir en Carpeta",
      selectFiles: "Seleccionar Archivos",
      fileFormatTip: "El formato de texto: *.txt",
      clearAll: "Limpiar Todo",
      doc: "Documentación",
      textRequired: "Por favor, ingrese el contenido del texto",
      pleaseWait: "Por favor, espere...",
      converting: "Convertiendo...",
      conversionFailed: "Conversión fallida"
    },
    options: {
      api: "Interfaz",
      selectApi: "Seleccionar API",
      language: "Idioma",
      selectLanguage: "Seleccionar Idioma",
      voice: "Voz",
      selectVoice: "Seleccionar Voz",
      speakingStyle: "Estilo de Habla",
      selectSpeakingStyle: "Seleccionar Estilo de Habla",
      rolePlaying: "Juego de Roles",
      selectRole: "Seleccionar Rol",
      speed: "Velocidad",
      pitch: "Tono",
      saveConfig: "Guardar Configuración",
      selectConfig: "Seleccionar Configuración",
      startConversion: "Iniciar Conversión",
      edgeApiWarning:
        "La interfaz de Edge no admite el corte automático y la longitud máxima del texto es desconocida. Por favor, procese manualmente el texto según sea necesario.",
      configureAzure:
        "Por favor, configure primero la clave y la región del servicio de voz de Azure.",
      saveSuccess: "Configuración guardada con éxito.",
      cancelSave: "Guardado cancelado.",
      inputWarning: "Por favor, introduzca el contenido del texto.",
      emptyListWarning: "La lista está vacía.",
    },
    lang: {
      AF_ZA: "Afrikáans (Sudáfrica)",
      AM_ET: "Amárico (Etiopía)",
      AR_AE: "Árabe (Emiratos Árabes Unidos)",
      AR_BH: "árabe (Bahrein)",
      AR_DZ: "árabe (Argelia)",
      AR_EG: "Árabe (Egipto)",
      AR_IL: "árabe (Israel)",
      AR_IQ: "árabe (Irak)",
      AR_JO: "árabe (Jordania)",
      AR_KW: "árabe (Kuwait)",
      AR_LB: "Árabe (Líbano)",
      AR_LY: "árabe (Libia)",
      AR_MA: "árabe (Marruecos)",
      AR_OM: "árabe (Omán)",
      AR_PS: "Árabe (Autoridad Palestina)",
      AR_QA: "árabe (Qatar)",
      AR_SA: "Árabe (Arabia Saudita)",
      AR_SY: "árabe (sirio)",
      AR_TN: "árabe (Túnez)",
      AR_YE: "árabe (Yemen)",
      AS_IN: "Asamés (India)",
      AZ_AZ: "Azerbaiyán (Azerbaiyán)",
      BG_BG: "búlgaro (Bulgaria)",
      BN_BD: "孟加拉语(孟加拉)",
      BN_IN: "孟加拉语(印度)",
      BS_BA: "波斯尼亚语(波斯尼亚和黑塞哥维那)",
      CA_ES: "加泰罗尼亚语(西班牙)",
      CS_CZ: "Checo(Checo)",
      CY_GB: "Galés (Reino Unido)",
      DA_DK: "danés (Dinamarca)",
      DE_AT: "alemán (Austria)",
      DE_CH: "Alemán (Suiza)",
      DE_DE: "Alemán (Alemania)",
      EL_GR: "Griego (Grecia)",
      EN_AU: "Inglés (Australia)",
      EN_CA: "Inglés (Canadá)",
      EN_GB: "Inglés (Reino Unido)",
      EN_GH: "Inglés (Ghana)",
      EN_HK: "Inglés (RAE de Hong Kong)",
      EN_IE: "inglés (Irlanda)",
      EN_IN: "Inglés (India)",
      EN_KE: "Inglés (Kenia)",
      EN_NG: "Inglés (Nigeria)",
      EN_NZ: "Inglés (Nueva Zelanda)",
      EN_PH: "Inglés (Filipinas)",
      EN_SG: "Inglés (Singapur)",
      EN_TZ: "Inglés (Tanzania)",
      EN_US: "Inglés (Estados Unidos)",
      EN_ZA: "Inglés (Sudáfrica)",
      ES_AR: "Spanish (Argentina)",
      ES_BO: "Spanish (Bolivia)",
      ES_CL: "Spanish (Chile)",
      ES_CO: "Spanish (Colombia)",
      ES_CR: "Spanish (Costa Rica)",
      ES_CU: "Spanish (Cuba)",
      ES_DO: "Spanish (Dominican Republic)",
      ES_EC: "Spanish (Ecuador)",
      ES_ES: "Spanish (Spain)",
      ES_GQ: "Spanish (Equatorial Guinea)",
      ES_GT: "Spanish (Guatemala)",
      ES_HN: "Spanish(Honduras)",
      ES_MX: "Spanish (México)",
      ES_NI: "Spanish(Nicaragua)",
      ES_PA: "Spanish (Panamá)",
      ES_PE: "Spanish (Perú)",
      ES_PR: "Spanish (Puerto Rico)",
      ES_PY: "Spanish(Paraguay)",
      ES_SV: "Spanish(El Salvador)",
      ES_US: "Spanish (Estados Unidos)",
      ES_UY: "Spanish (Uruguay)",
      ES_VE: "Spanish (Venezuela)",
      ET_EE: "Estonio (Estonia)",
      EU_ES: "euskera (euskera)",
      FA_IR: "persa (Irán)",
      FIL_PH: "Filipino (Filipinas)",
      FI_FI: "finlandés (Finlandia)",
      FR_BE: "Francés (Bélgica)",
      FR_CA: "Francés (Canadá)",
      FR_CH: "Francés (Suiza)",
      FR_FR: "Francés (Francia)",
      GA_IE: "irlandés (Irlanda)",
      GL_ES: "gallego (gallego)",
      GU_IN: "Gujarati (India)",
      HE_IL: "hebreo (Israel)",
      HI_IN: "Hindi(India)",
      HR_HR: "croata (croata)",
      HU_HU: "húngaro (Hungría)",
      HY_AM: "armenio (armenio)",
      ID_ID: "indonesio (Indonesia)",
      IS_IS: "islandés (Islandia)",
      IT_CH: "Italiano (Suiza)",
      IT_IT: "Italiano (Italia)",
      JA_JP: "japonés (Japón)",
      JV_ID: "javanés (Indonesia)",
      KA_GE: "georgiano (Georgia)",
      KK_KZ: "Kazajo (Kazajstán)",
      KM_KH: "jemer (Camboya)",
      KN_IN: "Canarés (India)",
      KO_KR: "coreano (Corea del Sur)",
      LO_LA: "Lao (Laos)",
      LT_LT: "lituano (Lituania)",
      LV_LV: "Letón (letón)",
      MK_MK: "Macedonio (Macedonia del Norte)",
      ML_IN: "马拉雅拉姆语(印度)",
      MN_MN: "mongol (mongol)",
      MR_IN: "maratí (India)",
      MS_MY: "Malayo (Malasia)",
      MT_MT: "Maltés (Malta)",
      MY_MM: "缅甸语(缅甸)",
      NB_NO: "书面挪威语(挪威)",
      NE_NP: "尼泊尔语(尼泊尔)",
      NL_BE: "Holandés (Bélgica)",
      NL_NL: "Holandés (Países Bajos)",
      OR_IN: "Odia (India)",
      PA_IN: "Punjabí (India)",
      PL_PL: "polaco (Polonia)",
      PS_AF: "Pashto (Afganistán)",
      PT_BR: "portugués (Brasil)",
      PT_PT: "portugués (Portugal)",
      RO_MD: "rumano(Molvador)",
      RO_RO: "rumano (Rumania)",
      RU_RU: "ruso (ruso)",
      SI_LK: "cingalés (Sri Lanka)",
      SK_SK: "eslovaco (Eslovaquia)",
      SL_SI: "Esloveno (Eslovenia)",
      SO_SO: "Somalí (Somalí)",
      SQ_AL: "Albanés (Albania)",
      SR_ME: "serbio (cirílico, montenegro)",
      SR_RS: "serbio (serbio)",
      SR_XK: "serbio (cirílico, Kosovo)",
      SU_ID: "Sundanés (Indonesia)",
      SV_SE: "sueco (Suecia)",
      SW_KE: "Suajili (Kenia)",
      SW_TZ: "Suajili (Tanzania)",
      TA_IN: "Tamil (India)",
      TA_LK: "Tamil (Sri Lanka)",
      TA_MY: "Tamil (Malasia)",
      TA_SG: "Tamil (Singapur)",
      TE_IN: "Telugu (India)",
      TH_TH: "Tailandés (Tailandia)",
      TR_TR: "Turco (Türkiye)",
      UK_UA: "ucraniano (ucraniano)",
      UR_IN: "Urdu (India)",
      UR_PK: "Urdu (Pakistán)",
      UZ_UZ: "uzbeko (Uzbekistán)",
      VI_VN: "vietnamita (Vietnam)",
      WUU_CN: "Chino (dialecto Wu, simplificado)",
      X_CUSTOM: "Idioma personalizado",
      YUE_CN: "Chino (cantonés, simplificado)",
      ZH_CN: "Chino (mandarín, simplificado)",
      ZH_CN_Bilingual: "Chino (mandarín, simplificado), inglés bilingüe",
      ZH_CN_HENAN: "Chino (mandarín Henan de las llanuras centrales, simplificado)",
      ZH_CN_LIAONING: "Chino (mandarín nororiental, simplificado)",
      ZH_CN_SHAANXI: "Chino (chino mandarín Shaanxi, simplificado)",
      ZH_CN_SHANDONG: "Chino (Jilu Mandarín, simplificado)",
      ZH_CN_SICHUAN: "Chino (mandarín del suroeste, simplificado)",
      ZH_HK: "Chino (cantonés, tradicional)",
      ZH_TW: "Chino (mandarín de Taiwán)",
      ZU_ZA: "祖鲁语(南非)",
      nalytics: "Análisis del lenguaje",
      onversationAnalysisPreviewHint:
        "通话摘要目前为封闭公共预览版，仅适用于已批准的资源。",
      fAudio: "Idioma del audio",
      esource: "Recurso de idioma",
      echnologiesUsed: "Tecnologías lingüísticas utilizadas",
      InPreview: "预览中的语言",
    },
    initialLocalStore: {
      audition:
        "Si piensas que este proyecto es bueno, Star, Fork y PR son bienvenidos. Tu Star es el mejor ánimo para el autor.",
    },
  },
  zh: {
    // Mensajes en chino
    aside: {
      text: "文本",
      batch: "批量",
      settings: "设置",
      documents: "文档",
    },
    messages: {
      saveConfig: "保存配置",
      saveConfigPrompt: "请输入配置名称",
      invalidInput: "输入无效",
      saveSuccess: "配置保存成功",
      cancelSave: "取消保存",
      inputWarning: "请输入文本内容",
      emptyListWarning: "列表为空"
    },
    buttons: {
      confirm: "确认",
      cancel: "取消"
    },
    version: {
      checkUpdate: "检查更新",
      currentVersion: "当前版本：",
      latestVersion: "最新版本：",
      updateAvailable: "有可用更新",
      noUpdate: "已是最新版本！",
      updateInfo: "更新信息",
      confirm: "确定",
      downloadLinks: "下载链接",
      password: "提取码: em1n",
    },
    bilibtn: {
      goToBilibili: "前往Bilibili",
    },
    configPage: {
      downloadPath: "下载路径",
      retryCount: "重试次数",
      retryInterval: "重试间隔 (秒)",
      speechKey: "Azure语音密钥",
      serviceRegion: "Azure服务区域",
      thirdPartyApi: "TTS88 API",
      thirdPartyApiPlaceholder: "例如: https://tts88.top/cognitiveservices/v1",
      tts88Key: "TTS88 密钥",
      tts88KeyPlaceholder: "请输入TTS88密钥",
      autoplay: "自动播放",
      language: "语言",
      updateNotification: "更新通知",
      titleStyle: "标题栏样式",
      auditionText: "试听文本",
      templateEdit: "模板编辑",
      name: "名称",
      action: "操作",
      delete: "删除",
      refreshConfig: "刷新配置",
      configFile: "配置文件",
      openLogs: "打开日志",
      clearLogs: "清除日志",
      yes: "是",
      no: "否",
      serviceRegionPlaceHolder: "填写服务区域，例如: eastasia",
      confirm: "确认",
      voice: "语音",
      style: "风格",
      role: "角色",
      speed: "语速",
      pitch: "音调",
      remove: "移除",
      openAIKey: "OpenAI密钥",
      openAIBaseUrl: "OpenAI基础URL",
      openAIBaseUrlPlaceholder: "例如: https://api.chatweb.plus/v1",
      gptModel: "GPT模型",
    },
    donate: {
      appreciation: "如果你觉得本项目不错，",
      encouragement: "欢迎Star、Fork和PR，你的Star是对作者最好的鼓励 :)",
      guideReminder: '如遇到任何问题，请仔细阅读"文档"→"用户指南"部分，包括"功能介绍"和"常见问题"。',
      feedback: '对于其他意见或建议，可以在"文档"→"加入Q群"中@群主或管理员，或私聊，或在GitHub或Gitee上提出issues。',
      buyCoffeeTitle: "请作者喝杯咖啡 🍻",
      wechatPayment: "使用微信付款",
      hoverForAlipay: "悬停查看支付宝付款",
      buyDrinkTitle: "请作者喝杯饮料 ☕️",
      alipayPayment: "使用支付宝付款",
      hoverForWechat: "移开鼠标查看微信付款",
    },
    footer: {
      downloadAudio: "下载音频",
      format: "格式",
    },
    styles: {
      assistant: "助手",
      chat: "聊天",
      customerservice: "客服",
      newscast: "新闻播报",
      affectionate: "深情",
      angry: "愤怒",
      calm: "冷静",
      cheerful: "欢快",
      disgruntled: "不满",
      fearful: "恐惧",
      gentle: "温柔",
      lyrical: "抒情",
      sad: "悲伤",
      serious: "严肃",
      embarrassed: "尴尬",
      depressed: "沮丧",
      envious: "嫉妒",
      Advertisement_upbeat: "广告推广",
      Sports_commentary: "体育评论",
      Sports_commentary_excited: "体育精彩解说",
      "documentary-narration": "纪录片解说",
      excited: "兴奋",
      friendly: "友好",
      terrified: "惊恐",
      shouting: "大喊",
      unfriendly: "不友好",
      whispering: "轻声细语",
      hopeful: "充满希望",
      "poetry-reading": "诗歌朗读",
      "narration-professional": "专业解说",
      "newscast-casual": "休闲新闻",
      "Narration-relaxed": "休闲旁白",
    },
    roles: {
      YoungAdultFemale: "青年女性",
      YoungAdultMale: "青年男性",
      OlderAdultFemale: "中年女性",
      OlderAdultMale: "中年男性",
      SeniorFemale: "老年女性",
      SeniorMale: "老年男性",
      Girl: "女孩",
      Boy: "男孩",
      Narrator: "旁白",
    },
    main: {
      titleGenerateTextGPT: "使用GPT生成文本",
      descriptionGenerateTextGPT: "使用GPT-3或GPT-4生成文本，这是世界上最强大的AI模型。",
      placeholderGPT: "请输入提示文本",
      action: "操作",
      textTab: "文本",
      ssmlTab: "SSML",
      placeholder: "请输入内容",
      fileName: "文件名",
      filePath: "文件路径",
      fileSize: "字数",
      status: "状态",
      ready: "准备",
      remove: "移除",
      play: "播放",
      openInFolder: "打开文件夹",
      selectFiles: "选择文件",
      fileFormatTip: "文本格式：*.txt",
      clearAll: "清空所有",
      doc: "文档",
      textRequired: "请输入文本内容",
      pleaseWait: "请稍候...",
      converting: "正在转换中...",
      conversionFailed: "转换失败"
    },
    options: {
      api: "接口",
      selectApi: "选择接口",
      language: "语言",
      selectLanguage: "选择语言",
      voice: "语音",
      selectVoice: "选择语音",
      speakingStyle: "风格",
      selectSpeakingStyle: "选择风格",
      rolePlaying: "角色",
      selectRole: "选择角色",
      speed: "语速",
      pitch: "音调",
      saveConfig: "保存配置",
      selectConfig: "选择配置",
      startConversion: "开始转换",
      edgeApiWarning: "Edge接口不支持自动分片，最大文本长度未知，请手动预处理文本。",
      configureAzure: "请先配置Azure的Speech服务密钥和区域。",
      saveSuccess: "配置保存成功。",
      cancelSave: "取消保存。",
      inputWarning: "请输入文本内容。",
      emptyListWarning: "列表为空。",
      waitMessage: "请稍候...",
      intensity: "强度",
      selectIntensity: "选择强度",
      default: "默认",
      weak: "弱",
      normal: "正常",
      strong: "强",
      extraStrong: "超强",
      silence: "静音",
      selectSilence: "选择静音",
      defaultSilence: "默认",
      volume: "音量",
      selectVolume: "选择音量",
      extraWeak: "极弱",
      medium: "中等",
      preset: "预设",
      selectPreset: "选择预设",
      presetDefault: "默认",
      presetNews: "新闻播报",
      presetStory: "故事讲述",
      presetPoetry: "诗歌朗诵",
      presetExcited: "兴奋活力",
      presetSad: "悲伤情绪",
      presetBusiness: "商务专业",
      presetFriendly: "亲和力",
      presetCustomerService: "客户服务",
      presetAdvertisement: "广告宣传",
      presetApplied: "已应用预设配置",
      voiceSettings: "语音设置",
      advancedSettings: "高级设置",
      saveAsPreset: "保存为预设",
      resetToDefault: "恢复默认",
      xSoft: "极轻",
      soft: "轻",
      loud: "响",
      xLoud: "极响",
      readyToConvert: "准备好了吗？",
      converting: "正在转换中...",
      switchToAnchors: "切换到主播模式",
      audioFormat: "音频格式",
      selectAudioFormat: "选择音频格式",
      audioQuality: "音频质量",
      selectAudioQuality: "选择音频质量",
      standardQuality: "标准质量",
      highQuality: "高质量",
      ultraQuality: "超高质量",
      autoPreview: "自动预览",
      autoPreviewDesc: "调整设置后自动播放预览",
      batchDownload: "批量下载",
      batchDownloadDesc: "下载所有已生成的音频",
      saveToCloud: "保存到云端",
      saveToCloudDesc: "将音频保存到云端以便随时访问",
      formatMP3: "MP3格式",
      formatWAV: "WAV格式",
      formatOGG: "OGG格式",
      formatFLAC: "FLAC格式"
    },
    lang: {
      AF_ZA: "南非荷兰语(南非)",
      AM_ET: "阿姆哈拉语(埃塞俄比亚)",
      AR_AE: "阿拉伯语(阿拉伯联合酋长国)",
      AR_BH: "阿拉伯语(巴林)",
      AR_DZ: "阿拉伯语(阿尔及利亚)",
      AR_EG: "阿拉伯语(埃及)",
      AR_IL: "阿拉伯语(以色列)",
      AR_IQ: "阿拉伯语(伊拉克)",
      AR_JO: "阿拉伯语(约旦)",
      AR_KW: "阿拉伯语(科威特)",
      AR_LB: "阿拉伯语(黎巴嫩)",
      AR_LY: "阿拉伯语(利比亚)",
      AR_MA: "阿拉伯语(摩洛哥)",
      AR_OM: "阿拉伯语(阿曼)",
      AR_PS: "阿拉伯语(巴勒斯坦民族权力机构)",
      AR_QA: "阿拉伯语(卡塔尔)",
      AR_SA: "阿拉伯语(沙特阿拉伯)",
      AR_SY: "阿拉伯语(叙利亚)",
      AR_TN: "阿拉伯语(突尼斯)",
      AR_YE: "阿拉伯语(也门)",
      AS_IN: "阿萨姆语(印度)",
      AZ_AZ: "阿塞拜疆语(阿塞拜疆) ",
      BG_BG: "保加利亚语(保加利亚)",
      BN_BD: "孟加拉语(孟加拉)",
      BN_IN: "孟加拉语(印度)",
      BS_BA: "波斯尼亚语(波斯尼亚和黑塞哥维那)",
      CA_ES: "加泰罗尼亚语(西班牙)",
      CS_CZ: "捷克语(捷克)",
      CY_GB: "威尔士语(英国)",
      DA_DK: "丹麦语(丹麦)",
      DE_AT: "德语(奥地利)",
      DE_CH: "德语(瑞士)",
      DE_DE: "德语(德国)",
      EL_GR: "希腊语(希腊)",
      EN_AU: "英语(澳大利亚)",
      EN_CA: "英语(加拿大)",
      EN_GB: "英语(英国)",
      EN_GH: "英语(加纳)",
      EN_HK: "英语(香港特别行政区)",
      EN_IE: "英语(爱尔兰)",
      EN_IN: "英语(印度)",
      EN_KE: "英语(肯尼亚)",
      EN_NG: "英语(尼日利亚)",
      EN_NZ: "英语(新西兰)",
      EN_PH: "英语(菲律宾)",
      EN_SG: "英语(新加坡)",
      EN_TZ: "英语(坦桑尼亚)",
      EN_US: "英语(美国)",
      EN_ZA: "英语(南非)",
      ES_AR: "西班牙语(阿根廷)",
      ES_BO: "西班牙语(玻利维亚)",
      ES_CL: "西班牙语(智利)",
      ES_CO: "西班牙语(哥伦比亚)",
      ES_CR: "西班牙语(哥斯达黎加)",
      ES_CU: "西班牙语(古巴)",
      ES_DO: "西班牙语(多米尼加共和国)",
      ES_EC: "西班牙语(厄瓜多尔)",
      ES_ES: "西班牙语(西班牙)",
      ES_GQ: "西班牙语(赤道几内亚)",
      ES_GT: "西班牙语(危地马拉)",
      ES_HN: "西班牙语(洪都拉斯)",
      ES_MX: "西班牙语(墨西哥)",
      ES_NI: "西班牙语(尼加拉瓜)",
      ES_PA: "西班牙语(巴拿马)",
      ES_PE: "西班牙语(秘鲁)",
      ES_PR: "西班牙语(波多黎各)",
      ES_PY: "西班牙语(巴拉圭)",
      ES_SV: "西班牙语(萨尔瓦多)",
      ES_US: "西班牙语(美国)",
      ES_UY: "西班牙语(乌拉圭)",
      ES_VE: "西班牙语(委内瑞拉)",
      ET_EE: "爱沙尼亚语(爱沙尼亚)",
      EU_ES: "巴斯克语(巴斯克语)",
      FA_IR: "波斯语(伊朗)",
      FIL_PH: "菲律宾语(菲律宾)",
      FI_FI: "芬兰语(芬兰)",
      FR_BE: "法语(比利时)",
      FR_CA: "法语(加拿大)",
      FR_CH: "法语(瑞士)",
      FR_FR: "法语(法国)",
      GA_IE: "爱尔兰语(爱尔兰)",
      GL_ES: "加利西亚语(加利西亚语)",
      GU_IN: "古吉拉特语(印度)",
      HE_IL: "希伯来语(以色列)",
      HI_IN: "印地语(印度)",
      HR_HR: "克罗地亚语(克罗地亚)",
      HU_HU: "匈牙利语(匈牙利)",
      HY_AM: "亚美尼亚语(亚美尼亚)",
      ID_ID: "印度尼西亚语(印度尼西亚)",
      IS_IS: "冰岛语(冰岛)",
      IT_CH: "意大利语(瑞士)",
      IT_IT: "意大利语(意大利)",
      JA_JP: "日语(日本)",
      JV_ID: "爪哇语(印度尼西亚)",
      KA_GE: "格鲁吉亚语(格鲁吉亚)",
      KK_KZ: "哈萨克语(哈萨克斯坦)",
      KM_KH: "高棉语(柬埔寨)",
      KN_IN: "埃纳德语(印度)",
      KO_KR: "韩语(韩国)",
      LO_LA: "老挝语(老挝) ",
      LT_LT: "立陶宛语(立陶宛)",
      LV_LV: "拉脱维亚语(拉脱维亚)",
      MK_MK: "马其顿语(北马其顿)",
      ML_IN: "马拉雅拉姆语(印度)",
      MN_MN: "蒙古语(蒙古)",
      MR_IN: "马拉地语(印度)",
      MS_MY: "马来语(马来西亚)",
      MT_MT: "马耳他语(马耳他)",
      MY_MM: "缅甸语(缅甸)",
      NB_NO: "书面挪威语(挪威)",
      NE_NP: "尼泊尔语(尼泊尔)",
      NL_BE: "荷兰语(比利时)",
      NL_NL: "荷兰语(荷兰)",
      OR_IN: "奥里亚语(印度)",
      PA_IN: "旁遮普语(印度)",
      PL_PL: "波兰语(波兰)",
      PS_AF: "普什图语(阿富汗)",
      PT_BR: "葡萄牙语(巴西)",
      PT_PT: "葡萄牙语(葡萄牙)",
      RO_MD: "罗马尼亚语(摩尔瓦多)",
      RO_RO: "罗马尼亚语(罗马尼亚)",
      RU_RU: "俄语(俄罗斯)",
      SI_LK: "僧伽罗语(斯里兰卡)",
      SK_SK: "斯洛伐克语(斯洛伐克)",
      SL_SI: "斯洛文尼亚语(斯洛文尼亚)",
      SO_SO: "索马里语(索马里)",
      SQ_AL: "阿尔巴尼亚语(阿尔巴尼亚)",
      SR_ME: "塞尔维亚语(西里尔文，黑山)",
      SR_RS: "塞尔维亚语(塞尔维亚)",
      SR_XK: "塞尔维亚语(西里尔语，科索沃)",
      SU_ID: "巽他语(印度尼西亚)",
      SV_SE: "瑞典语(瑞典)",
      SW_KE: "斯瓦希里语(肯尼亚)",
      SW_TZ: "斯瓦希里语(坦桑尼亚)",
      TA_IN: "泰米尔语(印度)",
      TA_LK: "泰米尔语(斯里兰卡)",
      TA_MY: "泰米尔语(马来西亚)",
      TA_SG: "泰米尔语(新加坡)",
      TE_IN: "泰卢固语(印度)",
      TH_TH: "泰语(泰国)",
      TR_TR: "土耳其语(Türkiye)",
      UK_UA: "乌克兰语(乌克兰)",
      UR_IN: "乌尔都语(印度)",
      UR_PK: "乌尔都语(巴基斯坦)",
      UZ_UZ: "乌兹别克语(乌兹别克斯坦)",
      VI_VN: "越南语(越南)",
      WUU_CN: "中文(吴语，简体)",
      X_CUSTOM: "自定义语言",
      YUE_CN: "中文(粤语，简体)",
      ZH_CN: "中文(普通话，简体)",
      ZH_CN_Bilingual: "中文(普通话，简体)，英语双语",
      ZH_CN_HENAN: "中文(中原官话河南，简体)",
      ZH_CN_LIAONING: "中文(东北官话，简体)",
      ZH_CN_SHAANXI: "中文(中原官话陕西，简体)",
      ZH_CN_SHANDONG: "中文(冀鲁官话，简体)",
      ZH_CN_SICHUAN: "中文(西南官话，简体)",
      ZH_HK: "中文(粤语，繁体)",
      ZH_TW: "中文(台湾普通话)",
      ZU_ZA: "祖鲁语(南非)",
      nalytics: "语言分析",
      onversationAnalysisPreviewHint:
        "通话摘要目前为封闭公共预览版，仅适用于已批准的资源。",
      fAudio: "Language of audio",
      esource: "语言资源",
      echnologiesUsed: "使用的语言技术",
      InPreview: "预览中的语言",
    },
    initialLocalStore: {
      audition:
        "如果你觉得这个项目还不错， 欢迎Star、Fork和PR。你的Star是对作者最好的鼓励。",
    },
  },
  // Otros idiomas...
};

// 根据浏览器语言或用户设置确定默认语言
const getDefaultLanguage = () => {
  // 从本地存储中读取用户设置的语言
  try {
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage) {
      const parsedLanguage = JSON.parse(savedLanguage);
      if (parsedLanguage === 'zh' || parsedLanguage === 'en') {
        return parsedLanguage;
      }
    }
    
    // 从config中读取
    const configLanguage = localStorage.getItem('config.language');
    if (configLanguage) {
      const parsedLanguage = JSON.parse(configLanguage);
      if (parsedLanguage === 'zh' || parsedLanguage === 'en') {
        return parsedLanguage;
      }
    }
  } catch (e) {
    console.error('读取语言设置失败:', e);
  }
  
  // 如果没有用户设置，则根据浏览器语言确定
  const browserLanguage = navigator.language.toLowerCase();
  if (browserLanguage.startsWith('zh')) {
    return 'zh';
  }
  
  // 默认使用中文
  return 'zh';
};

const defaultLanguage = getDefaultLanguage();

const i18n = createI18n({
  legacy: false, // 使用 Composition API 模式
  locale: defaultLanguage,
  fallbackLocale: 'zh', // 如果当前语言没有对应的翻译，则使用中文
  messages,
  silentTranslationWarn: import.meta.env.PROD, // 在生产环境中关闭翻译警告
  missingWarn: import.meta.env.DEV, // 在开发环境中启用缺失翻译警告
});

// 确保语言设置被正确应用
document.addEventListener('DOMContentLoaded', () => {
  console.log('当前应用的i18n语言:', i18n.global.locale.value);
  
  // 设置HTML lang属性
  document.documentElement.setAttribute('lang', i18n.global.locale.value);
});

export default i18n;
