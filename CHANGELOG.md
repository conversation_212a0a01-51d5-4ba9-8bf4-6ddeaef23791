## 2025-04-20

[v2.0.0](https://github.com/electron-vite/electron-vite-vue/pull/156)

这是一个基于原项目二次开发的新版本，从2.0.0版本开始重新编号。

主要功能更新：

1. **支持Web在线部署访问**
   * 项目支持直接部署到Web服务器，可通过浏览器在线访问使用
   * 不再局限于桌面应用，使用更加便捷
2. **修复Edge Speech API问题**
   * 修复了Edge本地朗读接口无法使用的问题
   * 优化了接口的稳定性和响应速度
   * 需要下载桌面端免费调用
3. **第三方中转TTS88 API支持**
   * 新增对TTS88 API的支持
   * 提供更多语音转换选择，提高转换质量和效率

## 2025-05-15

[v2.1.0]()

此版本主要对用户界面和交互体验进行了全面优化，提高了应用的易用性和视觉吸引力。

### 用户界面与交互改进：

1. **设置区域可折叠设计**
   * 添加了可折叠的设置面板，点击"语音设置"标题可展开/折叠
   * 折叠时节省界面空间，让用户专注于文本输入
   * 展开时可完整显示所有设置选项

2. **"开始转换"按钮位置优化**
   * 将按钮从页面底部移至顶部与标题同行
   * 解决用户需要滚动到页面底部才能点击按钮的问题
   * 无论用户在页面哪个位置，都能轻松开始转换过程

3. **状态反馈增强**
   * 添加了"准备好了吗？"和"正在转换中..."等状态提示文字
   * 按钮加载状态视觉反馈更明确
   * 完善的错误提示和成功通知机制

4. **视觉设计提升**
   * 按钮添加了微妙的光效动画，增强用户体验
   * 优化颜色和阴影效果，使界面更现代化
   * 统一了各组件的视觉风格

5. **响应式布局改进**
   * 优化了在不同屏幕尺寸下的显示效果
   * 移动设备上的交互体验更加友好
   * 设置区网格布局自动适应屏幕宽度

这次更新极大地提升了应用的用户体验，使TTS-Web-Vue更加易用、高效和美观。特别是"开始转换"按钮位置的优化，彻底解决了以往用户体验中的痛点问题。
