// Element Plus相关
declare module 'element-plus' {
  export const ElMessage: any;
  export const ElMessageBox: any;
}

// Element Plus图标
declare module '@element-plus/icons-vue' {
  export const Connection: any;
  export const ChatDotRound: any;
  export const Microphone: any;
  export const UserFilled: any;
  export const Mic: any;
  export const Aim: any;
  export const Timer: any;
  export const Headset: any;
  export const TrendCharts: any;
  export const Star: any;
  export const DocumentChecked: any;
  export const Reading: any;
  export const Collection: any;
  export const Lightning: any;
  export const Cloudy: any;
  export const Clock: any;
  export const RefreshRight: any;
  export const CaretRight: any;
  export const ArrowDown: any;
  export const Setting: any;
  export const Search: any;
  export const Avatar: any;
  export const Check: any;
  export const Close: any;
  export const Download: any;
}

// Pinia相关
declare module 'pinia' {
  export const storeToRefs: any;
}

// Vue-i18n相关
declare module 'vue-i18n' {
  export const useI18n: any;
} 